import { z } from 'zod';
import {
  getAuthCookiesWithLogging,
  AuthCookiesContext,
  createAuthCookiesContext,
} from '../auth/cookie-formatter.js';
import { getCurrentAuthInfo, getAuthCookiesFromContext } from '../auth/auth-context.js';
import { globalMcpUserTokenService } from '../auth/permission-service.js';

/**
 * Service module configuration interface
 */
interface ServiceModuleConfig {
  clientId: string;
  functions: Record<string, Function>;
}

/**
 * Service module type for better type safety
 */
type ServiceModules = Record<string, ServiceModuleConfig>;

/**
 * Get clientId for a specific service module
 * @param serviceModule - The service module name (e.g., 'oms', 'global')
 * @returns The clientId for the service module, or undefined if not found
 */
export function getServiceClientId(serviceModule: string): string | undefined {
  return SERVICE_MODULES[serviceModule]?.clientId;
}

/**
 * Get service functions for a specific service module
 * @param serviceModule - The service module name (e.g., 'oms', 'global')
 * @returns The functions object for the service module, or undefined if not found
 */
export function getServiceFunctions(serviceModule: string): Record<string, Function> | undefined {
  return SERVICE_MODULES[serviceModule]?.functions;
}

/**
 * Legacy compatibility: SERVICE_CLIENT_ID_MAPPING
 * @deprecated Use getServiceClientId() instead
 */
export const SERVICE_CLIENT_ID_MAPPING: Record<string, string> = new Proxy(
  {},
  {
    get(target, prop: string) {
      console.warn(
        `⚠️  SERVICE_CLIENT_ID_MAPPING['${prop}'] is deprecated. Use getServiceClientId('${prop}') instead.`
      );
      return getServiceClientId(prop);
    },
  }
);

const GetOrderDetailArgsSchema = z.object({
  vid: z.string().describe('Order VID (required)'),
  orderNO: z.string().optional().describe('Order number (optional)'),
  ikeaOrderNO: z.string().optional().describe('IKEA order number (optional)'),
});

const QueryOrderListsArgsSchema = z.object({
  page: z.number().min(1).default(1).describe('Page number (starting from 1)'),
  pageSize: z.number().min(1).max(100).default(10).describe('Number of items per page (1-100)'),
  orderStatus: z.string().optional().describe('Filter by order status'),
  storeCode: z.string().optional().describe('Filter by store code'),
  startDate: z.string().optional().describe('Start date filter (YYYY-MM-DD)'),
  endDate: z.string().optional().describe('End date filter (YYYY-MM-DD)'),
});

const VidArgsSchema = z.object({
  vid: z.string().describe('Order VID (required)'),
});

const StoreSubsidyAuditArgsSchema = z.object({
  storeId: z.string().describe('Store ID (required)'),
  fromTimeStamp: z.number().describe('Start timestamp in milliseconds (required)'),
  endTimeStamp: z.number().describe('End timestamp in milliseconds (required)'),
});

const EmptyArgsSchema = z.object({});
const TestEchoArgsSchema = z.object({
  message: z.string().optional().describe('Message to echo back'),
  data: z.any().optional().describe('Data to echo back'),
});

function safeStringify(obj: any, indent: number = 0): string {
  const seen = new WeakSet();
  const replacer = (_key: string, value: any) => {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) return '[Circular Reference]';
      seen.add(value);
    }
    if (value instanceof Error)
      return { name: value.name, message: value.message, stack: value.stack };
    if (typeof value === 'function') return '[Function]';
    if (value === undefined) return '[Undefined]';
    return value;
  };

  try {
    return JSON.stringify(obj, replacer, indent);
  } catch {
    return `[Object: ${typeof obj}]`;
  }
}

async function makeHttpRequest(
  url: string,
  method: string = 'POST',
  data?: any,
  authContext?: AuthCookiesContext,
  retryCount: number = 0,
  serviceModule?: string
): Promise<any> {
  let cookies: string;
  const currentAuthInfo = getCurrentAuthInfo();

  // Try to get auth cookies from request context first (preferred)
  const contextCookies = await getAuthCookiesFromContext(serviceModule);
  if (contextCookies) {
    cookies = contextCookies;
    console.error('🔐 [ServiceAdapter] Using auth cookies from request context');
  } else {
    // Fallback to explicit auth context or environment
    // IMPORTANT: Don't catch errors here - let OAuth2 token refresh handle auth failures
    const effectiveAuthContext =
      authContext || createAuthCookiesContext(currentAuthInfo || undefined, serviceModule);
    cookies = await getAuthCookiesWithLogging(effectiveAuthContext);
    console.error('🔐 [ServiceAdapter] Using auth cookies from explicit context or environment');
  }

  try {
    const result = await import('../utils/http.js').then(({ default: http }) =>
      http({
        url,
        method,
        data: data,
        useKong: true,
        cookies,
        timeout: 30000, // 30 second timeout for service calls
      })
    );
    return result;
  } catch (error: any) {
    // Check if we should refresh the token and retry (OAuth2 flow)
    if (
      retryCount === 0 &&
      currentAuthInfo &&
      globalMcpUserTokenService.shouldRefreshToken(error)
    ) {
      console.error(
        '🔄 [ServiceAdapter] Request failed with auth error, attempting token refresh...'
      );

      try {
        // Refresh the McpUserToken
        await globalMcpUserTokenService.refreshMcpUserToken(currentAuthInfo);

        // Retry the request with the new token (only once)
        console.error('🔄 [ServiceAdapter] Retrying request with refreshed token...');
        return makeHttpRequest(url, method, data, authContext, retryCount + 1, serviceModule);
      } catch (refreshError) {
        console.error('❌ [ServiceAdapter] Token refresh failed:', refreshError);
        // Fall through to throw the original error
      }
    }

    // If OAuth2 refresh failed or not applicable, check for fallback auth (testing scenarios)
    if (retryCount === 0 && !currentAuthInfo) {
      console.error(
        '⚠️ [ServiceAdapter] No OAuth2 context available, trying environment fallback...'
      );
      try {
        const { getGlobalConfig } = await import('../config/global-config.js');
        const env = getGlobalConfig();
        if (env.AUTH_COOKIES) {
          console.error('🔐 [ServiceAdapter] Using AUTH_COOKIES fallback for testing');
          // Retry with environment cookies (only once)
          // Create a proper auth context with environment cookies
          const fallbackAuthContext = createAuthCookiesContext(undefined, serviceModule);
          return makeHttpRequest(
            url,
            method,
            data,
            fallbackAuthContext,
            retryCount + 1,
            serviceModule
          );
        }
      } catch (envError) {
        console.error('❌ [ServiceAdapter] Environment fallback failed:', envError);
      }
    }

    // If all auth methods failed, throw the original error
    throw error;
  }
}

// Create a service function factory that includes auth context
function createServiceFunction(
  url: string,
  method: string = 'POST',
  dataTransform?: (data: any) => any,
  serviceModule?: string,
  urlTransform?: (url: string, data: any) => string
) {
  return async (data: any, authContext?: AuthCookiesContext) => {
    const finalUrl = urlTransform ? urlTransform(url, data) : url;
    const transformedData = dataTransform ? dataTransform(data) : data;
    return makeHttpRequest(finalUrl, method, transformedData, authContext, 0, serviceModule);
  };
}

export const SERVICE_MODULES: ServiceModules = {
  oms: {
    clientId: 'orders-portal',
    functions: {
      queryOrderLists: createServiceFunction(
        '/orders/search',
        'POST',
        (data: any) => ({
          page: data.page || 1,
          pageSize: data.pageSize || 10,
          orderStatus: data.orderStatus,
          storeCode: data.storeCode,
          startDate: data.startDate,
          endDate: data.endDate,
        }),
        'oms'
      ),

      getOrderDetail: createServiceFunction(
        '/order/detail',
        'POST',
        (data: any) => {
          const params = typeof data === 'string' ? { vid: data } : data;
          return {
            vid: params.vid,
            orderNO: params.orderNO,
            ikeaOrderNO: params.ikeaOrderNO,
          };
        },
        'oms'
      ),

      getLogisticsInfo: createServiceFunction(
        '/logistics',
        'GET',
        () => undefined, // No body for GET request
        'oms',
        (url, data) => `${url}/${data.vid}` // Add vid to URL path
      ),

      getAssemblingInfo: createServiceFunction(
        '/assembling',
        'GET',
        () => undefined, // No body for GET request
        'oms',
        (url, data) => `${url}/${data.vid}` // Add vid to URL path
      ),

      searchPermissionStore: createServiceFunction(
        '/user/searchPermissionStore',
        'GET',
        () => undefined, // No body for GET request
        'oms'
      ),

      getRetrievalData: createServiceFunction(
        '/order',
        'GET',
        () => undefined, // No body for GET request
        'oms',
        (url, data) => `${url}/${data.vid}/retrieval` // Add vid and retrieval to URL path
      ),

      queryStoreSubsidyAuditData: createServiceFunction(
        '/order-web/toolkit/storeSubsidyAuditData/query',
        'POST',
        (data: any) => ({
          storeId: data.storeId,
          fromTimeStamp: data.fromTimeStamp,
          endTimeStamp: data.endTimeStamp,
        }),
        'oms'
      ),

      getCurrentUser: createServiceFunction(
        '/user/current',
        'GET',
        () => undefined, // No body for GET request
        'oms'
      ),

      // === Tracking Tools (orders-portal) ===
      queryGsDailySales: createServiceFunction(
        '/cscOrders/queryDailySales/byGroup',
        'GET',
        () => undefined, // No body for GET request
        'oms'
      ),

      queryIbDailySales: createServiceFunction(
        '/cscOrders/queryIBDailySales/byGroup',
        'GET',
        () => undefined, // No body for GET request
        'oms'
      ),

      queryIdDailySales: createServiceFunction(
        '/cscOrders/queryIDDailySales/byGroup',
        'GET',
        () => undefined, // No body for GET request
        'oms'
      ),

      queryKitchenDailySales: createServiceFunction(
        '/cscOrders/queryKitchenDailySales/byGroup',
        'GET',
        () => undefined, // No body for GET request
        'oms'
      ),

      // === Invoice Center (orders-portal) ===
      // Readonly Tools
      queryInvoiceList: createServiceFunction(
        '/invoice/query/page',
        'POST',
        (data: any) => ({
          orderId: data.orderId,
          invoiceNo: data.invoiceNo,
          buyerName: data.buyerName,
          fromInvoiceTimeStamp: data.fromInvoiceTimeStamp,
          toInvoiceTimeStamp: data.toInvoiceTimeStamp,
          invoiceType: data.invoiceType,
          invoiceStatus: data.invoiceStatus,
          uploadState: data.uploadState,
          invoiceUse: data.invoiceUse,
          billingState: data.billingState,
          directInvoice: data.directInvoice,
          page: data.page || 1,
          size: data.size || 10,
        }),
        'oms'
      ),

      queryInvoiceDetail: createServiceFunction(
        '/invoice/order/detail',
        'POST',
        (data: any) => ({
          orderId: data.orderId,
          invoiceNo: data.invoiceNo,
        }),
        'oms'
      ),

      getCompanyTaxInfo: createServiceFunction(
        '/invoice/getCompanyTaxInfo',
        'GET',
        () => undefined, // No body for GET request
        'oms',
        (url, data) => `${url}?companyName=${encodeURIComponent(data.companyName)}`
      ),

      queryGoodsSupplementItem: createServiceFunction(
        '/invoice/getItemLineInfo',
        'GET',
        () => undefined, // No body for GET request
        'oms',
        (url, data) => `${url}?itemNo=${encodeURIComponent(data.itemNo)}`
      ),

      // Writing Tools
      manualInvoicing: createServiceFunction(
        '/invoice/manual',
        'POST',
        (data: any) => data, // Pass through all data for manual invoicing
        'oms'
      ),

      syncMergeBlueInvoice: createServiceFunction(
        '/invoice/syncMergeBlueInvoice',
        'POST',
        (data: any) => data, // Pass through all data for sync merge
        'oms'
      ),

      redraftInvoice: createServiceFunction(
        '/invoice/redraft',
        'POST',
        (data: any) => data, // Pass through all data for redraft
        'oms'
      ),

      cancelInvoice: createServiceFunction(
        '/invoice/cancel',
        'POST',
        (data: any) => data, // Pass through all data for cancel
        'oms'
      ),

      // === Price Compensation (orders-portal) ===
      // Readonly Tools
      queryPriceCompensationOrders: createServiceFunction(
        '/price-protection/search',
        'POST',
        (data: any) => ({
          vid: data.vid,
          contactPhoneNo: data.contactPhoneNo,
          applier: data.applier,
          auditor: data.auditor,
          compareStoreNo: data.compareStoreNo,
          orderStoreNo: data.orderStoreNo,
          status: data.status,
          applyDateStart: data.applyDateStart,
          applyDateEnd: data.applyDateEnd,
          completeDateStart: data.completeDateStart,
          completeDateEnd: data.completeDateEnd,
          curPage: data.curPage || 1,
          pageSize: data.pageSize || 10,
        }),
        'oms'
      ),

      queryPriceCompensationDetail: createServiceFunction(
        '/price-protection/detail',
        'POST',
        (data: any) => ({ vid: data.vid }),
        'oms'
      ),

      queryPriceCompensationDetailInfo: createServiceFunction(
        '/price-protection/recordInfo',
        'POST',
        (data: any) => ({ priceProtectionId: data.priceProtectionId }),
        'oms'
      ),

      priceCompare: createServiceFunction(
        '/price-protection/compare',
        'POST',
        (data: any) => ({
          vid: data.vid,
          storeNo: data.storeNo,
        }),
        'oms'
      ),

      downloadPriceCompensation: createServiceFunction(
        '/price-protection/download',
        'POST',
        (data: any) => data, // Pass through all data for download
        'oms'
      ),

      // Writing Tools
      priceCompensationRefund: createServiceFunction(
        '/price-protection/refund',
        'POST',
        (data: any) => ({
          vid: data.vid,
          storeNo: data.storeNo,
          sacId: data.sacId,
          paymentGatewayReferenceList: data.paymentGatewayReferenceList || [],
        }),
        'oms'
      ),

      updatePriceCompensation: createServiceFunction(
        '/price-protection/update',
        'POST',
        (data: any) => ({
          priceProtectionId: data.priceProtectionId,
          status: data.status,
        }),
        'oms'
      ),

      // === Refund Center (orders-portal) ===
      // Readonly Tools
      queryRefundOrders: createServiceFunction(
        '/refund/search',
        'POST',
        (data: any) => ({
          vid: data.vid,
          contactPhoneNo: data.contactPhoneNo,
          applier: data.applier,
          auditor: data.auditor,
          status: data.status,
          applyDateStart: data.applyDateStart,
          applyDateEnd: data.applyDateEnd,
          completeDateStart: data.completeDateStart,
          completeDateEnd: data.completeDateEnd,
          curPage: data.curPage || 1,
          pageSize: data.pageSize || 10,
        }),
        'oms'
      ),

      queryRefundDetail: createServiceFunction(
        '/refund/detail',
        'POST',
        (data: any) => ({ vid: data.vid }),
        'oms'
      ),

      queryRefundDetailInfo: createServiceFunction(
        '/refund/recordInfo',
        'POST',
        (data: any) => ({ refundId: data.refundId }),
        'oms'
      ),

      queryRefundProgress: createServiceFunction(
        '/refund/progress',
        'POST',
        (data: any) => ({ vid: data.vid }),
        'oms'
      ),

      downloadRefundData: createServiceFunction(
        '/refund/download',
        'POST',
        (data: any) => data, // Pass through all data for download
        'oms'
      ),

      // Writing Tools
      processRefund: createServiceFunction(
        '/refund/process',
        'POST',
        (data: any) => ({
          vid: data.vid,
          refundAmount: data.refundAmount,
          refundReason: data.refundReason,
          paymentGatewayReferenceList: data.paymentGatewayReferenceList || [],
        }),
        'oms'
      ),

      updateRefundStatus: createServiceFunction(
        '/refund/updateStatus',
        'POST',
        (data: any) => ({
          refundId: data.refundId,
          status: data.status,
        }),
        'oms'
      ),

      // === Refund Center Offline (orders-portal) ===
      // Readonly Tools
      queryOfflineRefundOrders: createServiceFunction(
        '/refund-offline/search',
        'POST',
        (data: any) => ({
          vid: data.vid,
          contactPhoneNo: data.contactPhoneNo,
          applier: data.applier,
          auditor: data.auditor,
          status: data.status,
          applyDateStart: data.applyDateStart,
          applyDateEnd: data.applyDateEnd,
          completeDateStart: data.completeDateStart,
          completeDateEnd: data.completeDateEnd,
          curPage: data.curPage || 1,
          pageSize: data.pageSize || 10,
        }),
        'oms'
      ),

      queryOfflineRefundDetail: createServiceFunction(
        '/refund-offline/detail',
        'POST',
        (data: any) => ({ vid: data.vid }),
        'oms'
      ),

      queryOfflineRefundDetailInfo: createServiceFunction(
        '/refund-offline/recordInfo',
        'POST',
        (data: any) => ({ refundId: data.refundId }),
        'oms'
      ),

      queryOfflineRefundProgress: createServiceFunction(
        '/refund-offline/progress',
        'POST',
        (data: any) => ({ vid: data.vid }),
        'oms'
      ),

      // Writing Tools
      processOfflineRefund: createServiceFunction(
        '/refund-offline/process',
        'POST',
        (data: any) => ({
          vid: data.vid,
          refundAmount: data.refundAmount,
          refundReason: data.refundReason,
        }),
        'oms'
      ),
    },
  },

  global: {
    clientId: 'mcp-mpc-odi', // Default clientId for global services
    functions: {},
  },

  test: {
    clientId: 'test-client', // Test clientId (not used for HTTP requests)
    functions: {
      ping: async (data: any) => ({
        success: true,
        message: 'pong',
        timestamp: new Date().toISOString(),
        data: data || null,
      }),

      echo: async (data: any) => ({
        success: true,
        message: 'echo response',
        timestamp: new Date().toISOString(),
        echo: data,
      }),
    },
  },
};

/**
 * Tool capability types for client elicitation
 */
export type ToolCapability = 'readonly' | 'write' | 'destructive';

/**
 * Tool risk levels for client handling
 */
export type ToolRiskLevel = 'safe' | 'moderate' | 'high';

/**
 * Tool configuration interface with elicitation metadata
 */
export interface ToolConfig {
  name: string;
  description: string;
  zodSchema: any;
  capability: ToolCapability; // What the tool can do
  riskLevel: ToolRiskLevel; // Risk assessment for client
  category?: string; // Functional category
  requiresConfirmation?: boolean; // Should client ask for confirmation
  tags?: string[]; // Additional metadata tags
}

/**
 * 🔧 工具配置
 */
export const SERVICE_TOOL_CONFIGS = {
  oms: [
    {
      name: 'queryOrderLists',
      description: 'Query order lists from OMS system',
      zodSchema: QueryOrderListsArgsSchema,
      capability: 'readonly' as ToolCapability,
      riskLevel: 'safe' as ToolRiskLevel,
      category: 'order-management',
      requiresConfirmation: false,
      tags: ['query', 'pagination', 'filter'],
    },
    {
      name: 'getOrderDetail',
      description: 'Get detailed information for a specific order',
      zodSchema: GetOrderDetailArgsSchema,
      capability: 'readonly' as ToolCapability,
      riskLevel: 'safe' as ToolRiskLevel,
      category: 'order-management',
      requiresConfirmation: false,
      tags: ['detail', 'order'],
    },
    {
      name: 'getLogisticsInfo',
      description: 'Get logistics information for an order',
      zodSchema: VidArgsSchema,
      capability: 'readonly' as ToolCapability,
      riskLevel: 'safe' as ToolRiskLevel,
      category: 'order-management',
      requiresConfirmation: false,
      tags: ['logistics', 'shipping'],
    },
    {
      name: 'getAssemblingInfo',
      description: 'Get assembling information for an order',
      zodSchema: VidArgsSchema,
      capability: 'readonly' as ToolCapability,
      riskLevel: 'safe' as ToolRiskLevel,
      category: 'order-management',
      requiresConfirmation: false,
      tags: ['assembly', 'furniture'],
    },
    {
      name: 'searchPermissionStore',
      description: 'Search permission stores for current user',
      zodSchema: EmptyArgsSchema,
      capability: 'readonly' as ToolCapability,
      riskLevel: 'safe' as ToolRiskLevel,
      category: 'user-management',
      requiresConfirmation: false,
      tags: ['permissions', 'stores'],
    },
    {
      name: 'getRetrievalData',
      description: 'Get retrieval data for an order',
      zodSchema: VidArgsSchema,
      capability: 'readonly' as ToolCapability,
      riskLevel: 'safe' as ToolRiskLevel,
      category: 'order-management',
      requiresConfirmation: false,
      tags: ['retrieval', 'pickup'],
    },
    {
      name: 'queryStoreSubsidyAuditData',
      description: 'Query store subsidy audit data for a specific store and time range',
      zodSchema: StoreSubsidyAuditArgsSchema,
    },
    {
      name: 'getCurrentUser',
      description: 'Get current user information',
      zodSchema: EmptyArgsSchema,
    },

    // === Tracking Tools (orders-portal) ===
    {
      name: 'queryGsDailySales',
      description: 'Query GS (General Store) daily sales data by group for monitoring and tracking',
      zodSchema: EmptyArgsSchema,
    },
    {
      name: 'queryIbDailySales',
      description: 'Query IB (IKEA Business) daily sales data by group for monitoring and tracking',
      zodSchema: EmptyArgsSchema,
    },
    {
      name: 'queryIdDailySales',
      description: 'Query ID (Indonesia) daily sales data by group for monitoring and tracking',
      zodSchema: EmptyArgsSchema,
    },
    {
      name: 'queryKitchenDailySales',
      description: 'Query Kitchen department daily sales data by group for monitoring and tracking',
      zodSchema: EmptyArgsSchema,
    },

    // === Invoice Center Tools ===
    {
      name: 'queryInvoiceList',
      description: 'Query invoice list with pagination and filtering options',
      zodSchema: z.object({
        orderId: z.string().optional(),
        invoiceNo: z.string().optional(),
        buyerName: z.string().optional(),
        fromInvoiceTimeStamp: z.string().optional(),
        toInvoiceTimeStamp: z.string().optional(),
        invoiceType: z.string().optional(),
        invoiceStatus: z.string().optional(),
        uploadState: z.string().optional(),
        invoiceUse: z.string().optional(),
        billingState: z.string().optional(),
        directInvoice: z.boolean().optional(),
        page: z.number().optional(),
        size: z.number().optional(),
      }),
    },
    {
      name: 'queryInvoiceDetail',
      description: 'Query detailed invoice information by order ID and invoice number',
      zodSchema: z.object({
        orderId: z.string(),
        invoiceNo: z.string(),
      }),
    },
    {
      name: 'getCompanyTaxInfo',
      description: 'Get company tax information by company name',
      zodSchema: z.object({
        companyName: z.string(),
      }),
    },
    {
      name: 'queryGoodsSupplementItem',
      description: 'Query goods supplement item information by item number',
      zodSchema: z.object({
        itemNo: z.string(),
      }),
    },
    {
      name: 'manualInvoicing',
      description: 'Process manual invoicing for orders',
      zodSchema: z.object({}).passthrough(), // Allow any properties for manual invoicing
      capability: 'write' as ToolCapability,
      riskLevel: 'moderate' as ToolRiskLevel,
      category: 'invoice-management',
      requiresConfirmation: true,
      tags: ['invoice', 'create', 'manual'],
    },
    {
      name: 'syncMergeBlueInvoice',
      description: 'Synchronize and merge blue invoice data',
      zodSchema: z.object({}).passthrough(), // Allow any properties for sync merge
      capability: 'write' as ToolCapability,
      riskLevel: 'moderate' as ToolRiskLevel,
      category: 'invoice-management',
      requiresConfirmation: true,
      tags: ['invoice', 'sync', 'merge'],
    },
    {
      name: 'redraftInvoice',
      description: 'Redraft an existing invoice',
      zodSchema: z.object({}).passthrough(), // Allow any properties for redraft
      capability: 'write' as ToolCapability,
      riskLevel: 'moderate' as ToolRiskLevel,
      category: 'invoice-management',
      requiresConfirmation: true,
      tags: ['invoice', 'modify', 'redraft'],
    },
    {
      name: 'cancelInvoice',
      description: 'Cancel an existing invoice',
      zodSchema: z.object({}).passthrough(), // Allow any properties for cancel
      capability: 'destructive' as ToolCapability,
      riskLevel: 'high' as ToolRiskLevel,
      category: 'invoice-management',
      requiresConfirmation: true,
      tags: ['invoice', 'cancel', 'destructive'],
    },

    // === Price Compensation Tools ===
    {
      name: 'queryPriceCompensationOrders',
      description: 'Query price compensation orders with filtering and pagination',
      zodSchema: z.object({
        vid: z.string().optional(),
        contactPhoneNo: z.string().optional(),
        applier: z.string().optional(),
        auditor: z.string().optional(),
        compareStoreNo: z.string().optional(),
        orderStoreNo: z.string().optional(),
        status: z.string().optional(),
        applyDateStart: z.string().optional(),
        applyDateEnd: z.string().optional(),
        completeDateStart: z.string().optional(),
        completeDateEnd: z.string().optional(),
        curPage: z.number().optional(),
        pageSize: z.number().optional(),
      }),
    },
    {
      name: 'queryPriceCompensationDetail',
      description: 'Query detailed price compensation information by VID',
      zodSchema: z.object({
        vid: z.string(),
      }),
    },
    {
      name: 'queryPriceCompensationDetailInfo',
      description: 'Query price compensation record information by protection ID',
      zodSchema: z.object({
        priceProtectionId: z.string(),
      }),
    },
    {
      name: 'priceCompare',
      description: 'Compare prices between stores for price protection',
      zodSchema: z.object({
        vid: z.string(),
        storeNo: z.string(),
      }),
    },
    {
      name: 'downloadPriceCompensation',
      description: 'Download price compensation data',
      zodSchema: z.object({}).passthrough(),
    },
    {
      name: 'priceCompensationRefund',
      description: 'Process price compensation refund',
      zodSchema: z.object({
        vid: z.string(),
        storeNo: z.string(),
        sacId: z.string(),
        paymentGatewayReferenceList: z.array(z.any()).optional(),
      }),
    },
    {
      name: 'updatePriceCompensation',
      description: 'Update price compensation status',
      zodSchema: z.object({
        priceProtectionId: z.string(),
        status: z.string(),
      }),
    },
  ],

  global: [],

  test: [
    {
      name: 'ping',
      description: 'Quick ping test - returns immediately',
      zodSchema: EmptyArgsSchema,
    },
    {
      name: 'echo',
      description: 'Echo test - returns input data',
      zodSchema: TestEchoArgsSchema,
    },
  ],
};

function createServiceToolHandler(moduleName: string, functionName: string, zodSchema?: any) {
  return async (args: any) => {
    const requestId = Math.random().toString(36).substring(2, 15);
    const timestamp = new Date().toISOString();

    try {
      let validatedArgs = args;
      if (zodSchema) {
        try {
          validatedArgs = zodSchema.parse(args);
        } catch (zodError: any) {
          return {
            success: false,
            error: 'Invalid arguments provided',
            details: { validationErrors: zodError.errors, receivedArgs: args },
            meta: { module: moduleName, function: functionName, requestId },
          };
        }
      }

      const serviceModule = (SERVICE_MODULES as any)[moduleName];
      if (!serviceModule) throw new Error(`Service module '${moduleName}' not found`);

      const serviceFunction = serviceModule.functions?.[functionName];
      if (!serviceFunction)
        throw new Error(`Function '${functionName}' not found in module '${moduleName}'`);

      const functionArgs =
        validatedArgs.data !== undefined
          ? validatedArgs.data
          : (() => {
              const args = { ...validatedArgs };
              delete args.cookies;
              return args;
            })();

      const result = await serviceFunction(functionArgs);

      return {
        success: true,
        data: result,
        meta: { module: moduleName, function: functionName, requestId, timestamp },
      };
    } catch (error: any) {
      // Enhanced error handling for timeout and large response issues
      let errorMessage = error.message;
      const errorDetails: any = { stack: error.stack, name: error.name };

      // Check for timeout errors
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        errorMessage = 'Request timed out - the service may be slow or returning large data';
        errorDetails.suggestion = 'Try using more specific filters to reduce response size';
        errorDetails.timeout = true;
      }

      // Check for large response errors
      if (
        error.message.includes('Maximum call stack size exceeded') ||
        error.message.includes('out of memory')
      ) {
        errorMessage = 'Response too large to process';
        errorDetails.suggestion = 'Use pagination or more specific filters';
        errorDetails.largeResponse = true;
      }

      return {
        success: false,
        error: errorMessage,
        details: errorDetails,
        meta: { module: moduleName, function: functionName, requestId, timestamp },
      };
    }
  };
}

function serializeResult(result: any): string {
  try {
    const serialized = safeStringify(result, 2);

    // Check response size and truncate if necessary
    const maxSize = 500 * 1024; // 500KB limit for MCP responses
    if (serialized.length > maxSize) {
      console.error(
        `⚠️ [ServiceAdapter] Large response detected: ${Math.round(serialized.length / 1024)}KB, truncating...`
      );

      // Create truncated response with summary
      const truncatedResult = {
        success: result?.success || false,
        data: result?.data
          ? {
              truncated: true,
              originalSize: `${Math.round(serialized.length / 1024)}KB`,
              summary: Array.isArray(result.data)
                ? `Array with ${result.data.length} items (showing first 3)`
                : 'Object (truncated)',
              preview: Array.isArray(result.data)
                ? result.data.slice(0, 3)
                : typeof result.data === 'object'
                  ? Object.keys(result.data)
                      .slice(0, 10)
                      .reduce((obj: any, key) => {
                        obj[key] = (result.data as any)[key];
                        return obj;
                      }, {})
                  : result.data,
            }
          : result?.data,
        meta: result?.meta || {},
        warning:
          'Response was truncated due to size limits. Use more specific filters to get complete data.',
      };

      return safeStringify(truncatedResult, 2);
    }

    return serialized;
  } catch {
    return safeStringify(
      {
        success: result?.success || false,
        error: result?.error || 'Serialization failed',
        meta: result?.meta || {},
      },
      2
    );
  }
}

export function registerAllServiceTools(server: any) {
  const totalTools = 0;

  Object.entries(SERVICE_TOOL_CONFIGS).forEach(([moduleName, functions]) => {
    functions.forEach(func => {
      const toolName = `${moduleName}_${func.name}`;
      const handler = createServiceToolHandler(moduleName, func.name, (func as any).zodSchema);
      const zodSchema = (func as any).zodSchema;

      const toolHandler = async (args: any) => {
        const result = await handler(args);
        return { content: [{ type: 'text', text: serializeResult(result) }] };
      };

      try {
        if (zodSchema) {
          server.tool(toolName, func.description, zodSchema, toolHandler);
        } else {
          server.tool(toolName, func.description, toolHandler);
        }
      } catch (error: any) {
        console.error(`Failed to register tool: ${toolName} - ${error.message}`);
      }
    });
  });

  console.error(`Registered ${totalTools} service tools`);
  return totalTools;
}
