import { getGlobalConfig } from '../config/global-config.js';
import { OAuthClientInformationFull } from '@modelcontextprotocol/sdk/shared/auth.js';

/**
 * OAuth2 Configuration for Keycloak Integration
 */
export interface OAuth2Config {
  enabled: boolean;
  keycloak: {
    baseUrl: string;
    realm: string;
    authUrl: string;
    tokenUrl: string;
    userInfoUrl: string;
    jwksUrl: string;
    revocationUrl: string;
    registrationUrl: string;
    issuer: string;
  };
  // Note: Client parameters (id, redirect_uri, scopes) are provided by clients in requests
  // Only server-side authentication configuration is stored here
  serverAuth: {
    secret: string;
    authMethod: 'client_secret_basic' | 'none';
  };
  server: {
    baseUrl: string;
    port: number;
    issuerUrl: string;
  };
}

/**
 * Get OAuth2 configuration from global config store
 * Uses proper configuration hierarchy: NACOS > Helm > process.env > defaults
 */
export function getOAuth2Config(): OAuth2Config {
  const env = getGlobalConfig();

  const keycloakBaseUrl = env.KEYCLOAK_BASE_URL;
  const realm = env.KEYCLOAK_REALM;
  const realmUrl = `${keycloakBaseUrl}/realms/${realm}`;

  return {
    enabled: env.OAUTH2_ENABLED,
    keycloak: {
      baseUrl: keycloakBaseUrl,
      realm: realm,
      authUrl: `${realmUrl}/protocol/openid-connect/auth`,
      tokenUrl: `${realmUrl}/protocol/openid-connect/token`,
      userInfoUrl: `${realmUrl}/protocol/openid-connect/userinfo`,
      jwksUrl: `${realmUrl}/protocol/openid-connect/certs`,
      revocationUrl: `${realmUrl}/protocol/openid-connect/revoke`,
      registrationUrl: `${realmUrl}/clients-registrations/openid-connect`,
      issuer: realmUrl,
    },
    serverAuth: {
      secret: env.OAUTH2_CLIENT_SECRET,
      authMethod: env.OAUTH2_CLIENT_AUTH_METHOD,
    },
    server: {
      baseUrl: env.MCP_SERVER_PUBLIC_BASE_URL,
      port: env.MCP_SERVER_PORT,
      issuerUrl: env.OAUTH2_ISSUER_URL,
    },
  };
}

/**
 * Validate OAuth2 configuration
 */
export function validateOAuth2Config(config: OAuth2Config): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!config.enabled) {
    return { valid: true, errors: [] };
  }

  // Validate Keycloak configuration
  if (!config.keycloak.baseUrl) {
    errors.push('Keycloak base URL is required');
  }

  if (!config.keycloak.realm) {
    errors.push('Keycloak realm is required');
  }

  // Validate server authentication configuration
  // Note: client_id, redirect_uri, and scopes are now provided by clients in requests

  // Client secret is only required for client_secret_basic auth method
  if (config.serverAuth.authMethod === 'client_secret_basic' && !config.serverAuth.secret) {
    errors.push('OAuth2 client secret is required for client_secret_basic auth method');
  }

  // Validate server configuration
  if (!config.server.baseUrl) {
    errors.push('MCP server base URL is required');
  }

  if (!config.server.port || config.server.port <= 0) {
    errors.push('MCP server port must be a positive number');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Log OAuth2 configuration (hiding sensitive information)
 */
export function logOAuth2Config(config: OAuth2Config) {
  console.error('🔐 OAuth2 Configuration:');
  console.error(`  Enabled: ${config.enabled}`);

  if (!config.enabled) {
    console.error('  OAuth2 is disabled');
    return;
  }

  console.error('  Keycloak:');
  console.error(`    Base URL: ${config.keycloak.baseUrl}`);
  console.error(`    Realm: ${config.keycloak.realm}`);
  console.error(`    Issuer: ${config.keycloak.issuer}`);
  console.error(`    Auth URL: ${config.keycloak.authUrl}`);
  console.error(`    Token URL: ${config.keycloak.tokenUrl}`);
  console.error(`    JWKS URL: ${config.keycloak.jwksUrl}`);

  console.error('  Server Authentication:');
  console.error(`    Secret: ${config.serverAuth.secret ? '***SET***' : '***NOT SET***'}`);
  console.error(`    Auth Method: ${config.serverAuth.authMethod}`);
  console.error('    Note: client_id, redirect_uri, and scopes provided by clients in requests');

  console.error('  Server:');
  console.error(`    Base URL: ${config.server.baseUrl}`);
  console.error(`    Port: ${config.server.port}`);
  console.error(`    Issuer URL: ${config.server.issuerUrl}`);
}

/**
 * Get OAuth2 endpoints for Keycloak
 */
export function getKeycloakEndpoints(config: OAuth2Config) {
  return {
    authorizationUrl: config.keycloak.authUrl,
    tokenUrl: config.keycloak.tokenUrl,
    revocationUrl: config.keycloak.revocationUrl,
    registrationUrl: config.keycloak.registrationUrl,
  };
}

/**
 * Generate common localhost redirect URIs for MCP clients
 */
function generateLocalhostRedirectUris(): string[] {
  const uris: string[] = [];

  // Common MCP client port ranges
  const commonPorts = [3000, 6274, 6275, 6276, 6277, 6278, 6279, 6280, 8080, 8081, 8082];
  const paths = ['/oauth/callback', '/auth/callback'];

  for (const port of commonPorts) {
    for (const path of paths) {
      uris.push(`http://localhost:${port}${path}`);
    }
  }

  return uris;
}

/**
 * Create a client information provider function for OAuth2 configuration
 */
export function createOAuth2ClientProvider(config: OAuth2Config) {
  return async (clientId: string): Promise<OAuthClientInformationFull | undefined> => {
    // Dynamic client registration for development
    // In development/test environments, we allow any non-empty client_id
    // In production, you might want to implement proper client registration
    const env = getGlobalConfig();

    if (!clientId || clientId.trim() === '') {
      return undefined; // Empty client_id is always invalid
    }

    // For development environments, allow any client_id
    if (env.NODE_ENV === 'development' || env.NODE_ENV === 'test') {
      console.log(`🔧 [OAuth2] Allowing client_id '${clientId}' in ${env.NODE_ENV} environment`);

      // Generate comprehensive redirect URIs for development
      const allowedRedirectUris = [
        // Common MCP client ports and patterns for development
        ...generateLocalhostRedirectUris(),
        // Add server-based fallbacks
        `${config.server.baseUrl}/auth/callback`, // Server port fallback
      ];

      const clientInfo: OAuthClientInformationFull = {
        client_id: clientId, // Use the client-provided ID
        redirect_uris: allowedRedirectUris,
        grant_types: ['authorization_code', 'refresh_token'],
        response_types: ['code'],
        scope: 'openid profile email', // Default scopes - clients can request specific ones
        token_endpoint_auth_method: config.serverAuth.authMethod,
      };

      // Only include client_secret for confidential clients
      if (config.serverAuth.authMethod === 'client_secret_basic') {
        clientInfo.client_secret = config.serverAuth.secret;
      }

      return clientInfo;
    }

    // For production environments, use stricter validation
    // Allow known MCP client patterns and common AI clients
    const isKnownClient = (
      clientId.startsWith('mcp-') ||
      clientId === 'mcp-server' ||
      clientId.startsWith('ai-') ||
      clientId.startsWith('claude-') ||
      clientId.startsWith('gpt-') ||
      clientId.startsWith('anthropic-') ||
      clientId === 'ai-genie' ||
      clientId === 'claude-desktop'
    );

    if (isKnownClient) {
      console.log(`🔧 [OAuth2] Allowing known client_id '${clientId}' in ${env.NODE_ENV} environment`);

      const allowedRedirectUris = [
        ...generateLocalhostRedirectUris(),
        `${config.server.baseUrl}/auth/callback`,
      ];

      const clientInfo: OAuthClientInformationFull = {
        client_id: clientId,
        redirect_uris: allowedRedirectUris,
        grant_types: ['authorization_code', 'refresh_token'],
        response_types: ['code'],
        scope: 'openid profile email',
        token_endpoint_auth_method: config.serverAuth.authMethod,
      };

      if (config.serverAuth.authMethod === 'client_secret_basic') {
        clientInfo.client_secret = config.serverAuth.secret;
      }

      return clientInfo;
    }

    console.warn(`⚠️ [OAuth2] Rejecting unknown client_id '${clientId}' in ${env.NODE_ENV} environment`);
    return undefined;
  };
}
